import { Component, Event, EventEmitter, State, Listen, h, Element } from '@stencil/core';
import { FrontendLogger } from '../../../global/script/var';

/**
 * Component for creating SenseQuery categories
 */
@Component({
  tag: 'p-category-form',
  styleUrl: 'p-category-form.css',
  shadow: true,
})
export class PCategoryForm {
  @Element() el: HTMLElement;

  @Event({
    eventName: 'addSenseQueryCategoryFromModal',
    bubbles: true,
  })
  addSenseQueryCategoryFromModalEventEmitter: EventEmitter;

  @Event({
    eventName: 'modalCloseEvent',
    bubbles: true,
  })
  modalCloseEventEmitter: EventEmitter;

  @State() categoryValue: string = '';
  @State() formErrors: { [key: string]: string } = {};

  componentDidLoad() {
    // Focus on the category input when modal opens
    const categoryInput = this.el.shadowRoot?.querySelector(
      'e-input[name="categoryValue"]',
    ) as HTMLElement;
    if (categoryInput) {
      setTimeout(() => categoryInput.focus(), 100);
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value } = event.detail;

    if (name === 'categoryValue') {
      this.categoryValue = value;
      // Clear error when user starts typing
      if (this.formErrors.category) {
        this.formErrors = { ...this.formErrors };
        delete this.formErrors.category;
      }
    }
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'saveCategory') {
      this.saveCategory();
    } else if (event.detail.action === 'cancelCategory') {
      this.closeModal();
    }
  }

  private validateForm(): boolean {
    const errors: { [key: string]: string } = {};

    // Validate category (required)
    if (!this.categoryValue.trim()) {
      errors.category = 'Category is required';
    } else if (this.categoryValue.trim().length < 2) {
      errors.category = 'Category must be at least 2 characters long';
    } else if (this.categoryValue.trim().length > 50) {
      errors.category = 'Category must be less than 50 characters';
    }

    this.formErrors = errors;
    return Object.keys(errors).length === 0;
  }

  private saveCategory() {
    if (!this.validateForm()) {
      return;
    }

    FrontendLogger.debug(
      'Emitting addSenseQueryCategoryFromModal with:',
      this.categoryValue.trim(),
    );

    this.addSenseQueryCategoryFromModalEventEmitter.emit({
      category: this.categoryValue.trim(),
    });
  }

  private closeModal() {
    this.modalCloseEventEmitter.emit();
  }

  render() {
    return (
      <div class="category-form">
        <e-text>
          <strong>
            Enter Category Name <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.25}></l-spacer>
        <e-input
          type="text"
          name="categoryValue"
          placeholder="e.g. Product, Pricing, Installation"
          value={this.categoryValue}
        ></e-input>
        {this.formErrors.category && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.category}</e-text>
          </div>
        )}
        <l-spacer value={3}></l-spacer>

        <div class="form-actions">
          <e-button variant="light" action="cancelCategory">
            Cancel
          </e-button>
          <e-button action="saveCategory" disabled={!this.categoryValue.trim()}>
            Add
          </e-button>
        </div>
      </div>
    );
  }
}
